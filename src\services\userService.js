const API_BASE_URL = 'https://jsonplaceholder.typicode.com';

/**
 * @param {number} page 
 * @param {number} limit 
 * @param {string} search 
 * @returns {Promise<{users: Array, total: number, page: number, totalPages: number}>}
 */
export const fetchUsers = async (page = 1, limit = 6, search = '') => {
  try {
    const response = await fetch(`${API_BASE_URL}/users`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const allUsers = await response.json();
    
    const filteredUsers = search 
      ? allUsers.filter(user => 
          user.name.toLowerCase().includes(search.toLowerCase())
        )
      : allUsers;
    
    const total = filteredUsers.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    const users = filteredUsers.slice(startIndex, endIndex);
    
    return {
      users,
      total,
      page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

/**
 * @param {number} userId 
 * @returns {Promise<Object>} 
 */
export const fetchUserById = async (userId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/users/${userId}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};
