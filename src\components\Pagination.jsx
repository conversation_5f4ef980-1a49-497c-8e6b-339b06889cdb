
/**
 * Pagination component for navigating through pages
 * @param {number} currentPage
 * @param {number} totalPages
 * @param {Function} onPageChange
 * @param {boolean} hasNextPage
 * @param {boolean} hasPrevPage 
 */
const Pagination = ({
  currentPage,
  totalPages,
  onPageChange,
  hasNextPage,
  hasPrevPage
}) => {
  if (totalPages <= 1) {
    return null;
  }

  const handlePageClick = (page) => {
    if (page !== currentPage && page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (hasPrevPage) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (hasNextPage) {
      onPageChange(currentPage + 1);
    }
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(totalPages, currentPage + 2);

      if (currentPage <= 3) {
        endPage = Math.min(totalPages, 5);
      }
      if (currentPage >= totalPages - 2) {
        startPage = Math.max(1, totalPages - 4);
      }

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="text-center">
      <nav aria-label="User pagination" className="d-inline-block">
        <ul className="pagination justify-content-center mb-3">
          <li className={`page-item ${!hasPrevPage ? 'disabled' : ''}`}>
            <button
              className="page-link"
              onClick={handlePrevious}
              disabled={!hasPrevPage}
              aria-label="Previous page"
            >
              <i className="bi bi-chevron-left"></i>
              <span className="d-none d-sm-inline ms-1">Previous</span>
            </button>
          </li>

          {pageNumbers.map((page, index) => (
            <li
              key={index}
              className={`page-item ${page === currentPage ? 'active' : ''} ${page === '...' ? 'disabled' : ''}`}
            >
              {page === '...' ? (
                <span className="page-link">...</span>
              ) : (
                <button
                  className="page-link"
                  onClick={() => handlePageClick(page)}
                  aria-label={`Go to page ${page}`}
                  aria-current={page === currentPage ? 'page' : undefined}
                >
                  {page}
                </button>
              )}
            </li>
          ))}

          <li className={`page-item ${!hasNextPage ? 'disabled' : ''}`}>
            <button
              className="page-link"
              onClick={handleNext}
              disabled={!hasNextPage}
              aria-label="Next page"
            >
              <span className="d-none d-sm-inline me-1">Next</span>
              <i className="bi bi-chevron-right"></i>
            </button>
          </li>
        </ul>
      </nav>

      <div className="text-center">
        <small className="text-white-50">
          Page {currentPage} of {totalPages}
        </small>
      </div>
    </div>
  );
};

export default Pagination;
