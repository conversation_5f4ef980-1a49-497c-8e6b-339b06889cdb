import { useState, useEffect } from 'react';

/**
 * SearchInput component for filtering users by name
 * @param {Function} onSearch 
 * @param {string} placeholder 
 * @param {number} debounceMs 
 */
const SearchInput = ({
  onSearch,
  placeholder = "Search users by name...",
  debounceMs = 300
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onSearch(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, onSearch, debounceMs]);

  const handleInputChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
  };

  return (
    <div className="row justify-content-center">
      <div className="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
        <div className="input-group">
          <span className="input-group-text">
            <i className="bi bi-search"></i>
          </span>
          <input
            type="text"
            className="form-control"
            placeholder={placeholder}
            value={searchTerm}
            onChange={handleInputChange}
            aria-label="Search users"
          />
          {searchTerm && (
            <button
              className="btn btn-outline-secondary"
              type="button"
              onClick={handleClearSearch}
              aria-label="Clear search"
              title="Clear search"
            >
              <i className="bi bi-x-lg"></i>
            </button>
          )}
        </div>
        {searchTerm && (
          <small className="text-white-50 mt-2 d-block text-center">
            Searching for: <strong>"{searchTerm}"</strong>
          </small>
        )}
      </div>
    </div>
  );
};

export default SearchInput;
