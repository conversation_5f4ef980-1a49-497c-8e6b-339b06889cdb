import { useState, useEffect, useCallback } from 'react';
import { fetchUsers } from '../services/userService';
import UserCard from './UserCard';
import SearchInput from './SearchInput';
import Pagination from './Pagination';


const Dashboard = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  });

  const usersPerPage = 6;

  const loadUsers = useCallback(async (page = 1, search = '') => {
    try {
      setLoading(true);
      setError(null);

      const result = await fetchUsers(page, usersPerPage, search);

      setUsers(result.users);
      setPagination({
        total: result.total,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage
      });
    } catch (err) {
      setError('Failed to load users. Please try again later.');
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  }, [usersPerPage]);

  useEffect(() => {
    loadUsers(currentPage, searchTerm);
  }, [currentPage, searchTerm, loadUsers]);

  const handleSearch = useCallback((search) => {
    setSearchTerm(search);
    setCurrentPage(1);
  }, []);

  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleRetry = () => {
    loadUsers(currentPage, searchTerm);
  };

  return (
    <div className="container-fluid" style={{position: 'relative', zIndex: 10}}>
      <div className="row justify-content-center mb-3 mb-md-4">
        <div className="col-12 col-xl-10">
          <div className="dashboard-header text-center">
            <h1 className="text-3xl dashboard-title mb-2 mb-md-3">
              <i className="bi bi-people-fill me-2 me-md-3" style={{fontSize: 'clamp(1.5rem, 4vw, 2.5rem)'}}></i>
              <span className="d-none d-sm-inline">User Dashboard</span>
              <span className="d-inline d-sm-none">Users</span>
            </h1>
            <p className="dashboard-subtitle text-lg mb-0">
              <span className="d-none d-md-inline">Discover and connect with users from around the world</span>
              <span className="d-inline d-md-none">Connect with amazing users</span>
            </p>
          </div>
        </div>
      </div>

      <div className="row justify-content-center mb-3 mb-md-4">
        <div className="col-12 col-xl-10">
          <div className="search-container">
            <SearchInput onSearch={handleSearch} />
          </div>
        </div>
      </div>

      {loading && (
        <div className="row justify-content-center">
          <div className="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
            <div className="loading-container text-center">
              <div className="spinner-border text-light" role="status" style={{width: 'clamp(2rem, 5vw, 4rem)', height: 'clamp(2rem, 5vw, 4rem)'}}>
                <span className="visually-hidden">Loading users...</span>
              </div>
              <p className="mt-3 text-white mb-0 text-base">
                <span className="d-none d-md-inline">Loading amazing users...</span>
                <span className="d-inline d-md-none">Loading...</span>
              </p>
            </div>
          </div>
        </div>
      )}

      {error && !loading && (
        <div className="row justify-content-center">
          <div className="col-12 col-sm-10 col-md-8 col-lg-6">
            <div className="alert alert-danger text-center" role="alert">
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              <span className="fs-responsive-md">{error}</span>
              <br />
              <button
                className="btn btn-outline-danger mt-3"
                onClick={handleRetry}
              >
                <i className="bi bi-arrow-clockwise me-1"></i>
                <span className="d-none d-sm-inline">Try Again</span>
                <span className="d-inline d-sm-none">Retry</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {!loading && !error && (
        <>
          {users.length > 0 ? (
            <>
              <div className="row mb-2 mb-md-3">
                <div className="col-12">
                  <div className="results-info fs-responsive-md">
                    {searchTerm ? (
                      <>
                        <span className="d-none d-md-inline">Found </span>
                        <strong>{pagination.total}</strong> user{pagination.total !== 1 ? 's' : ''}
                        <span className="d-none d-sm-inline"> matching "</span>
                        <span className="d-inline d-sm-none"> for "</span>
                        <strong className="text-truncate-mobile d-inline-block" style={{maxWidth: '150px'}}>{searchTerm}</strong>"
                      </>
                    ) : (
                      <>
                        <span className="d-none d-md-inline">Showing </span>
                        <strong>{pagination.total}</strong>
                        <span className="d-none d-sm-inline"> amazing</span> user{pagination.total !== 1 ? 's' : ''}
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="row justify-content-start" style={{width: '100%', margin: 0}}>
                {users.map((user) => (
                  <UserCard key={user.id} user={user} />
                ))}
              </div>

              <div className="pagination-container">
                <Pagination
                  currentPage={currentPage}
                  totalPages={pagination.totalPages}
                  onPageChange={handlePageChange}
                  hasNextPage={pagination.hasNextPage}
                  hasPrevPage={pagination.hasPrevPage}
                />
              </div>
            </>
          ) : (
            <div className="no-results">
              <i className="bi bi-search display-1 mb-3 d-none d-md-block"></i>
              <i className="bi bi-search display-4 mb-3 d-block d-md-none"></i>
              <h3 className="text-white mb-3 fs-responsive-lg">
                <span className="d-none d-md-inline">No users found</span>
                <span className="d-inline d-md-none">No users</span>
              </h3>
              <p className="text-white-50 mb-4 fs-responsive-md">
                {searchTerm ? (
                  <>
                    <span className="d-none d-md-inline">No users match your search for "</span>
                    <span className="d-inline d-md-none">No match for "</span>
                    <strong className="text-truncate-mobile d-inline-block" style={{maxWidth: '200px'}}>{searchTerm}</strong>".
                    <br className="d-none d-md-block" />
                    <span className="d-none d-md-inline">Try adjusting your search terms.</span>
                    <span className="d-inline d-md-none"> Try different terms.</span>
                  </>
                ) : (
                  <>
                    <span className="d-none d-md-inline">No users available at the moment.</span>
                    <span className="d-inline d-md-none">No users available.</span>
                  </>
                )}
              </p>
              {searchTerm && (
                <button
                  className="btn btn-outline-primary"
                  onClick={() => handleSearch('')}
                >
                  <i className="bi bi-arrow-left me-1"></i>
                  <span className="d-none d-sm-inline">Show All Users</span>
                  <span className="d-inline d-sm-none">Show All</span>
                </button>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Dashboard;
