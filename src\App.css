/* Global Reset & Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  width: 100%;
  overflow-x: hidden;
}

body {
  margin: 0;
  padding: 0;
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: clamp(14px, 2vw, 16px);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Responsive text utilities */
.text-xs { font-size: clamp(0.75rem, 1.5vw, 0.875rem); }
.text-sm { font-size: clamp(0.875rem, 2vw, 1rem); }
.text-base { font-size: clamp(1rem, 2.5vw, 1.125rem); }
.text-lg { font-size: clamp(1.125rem, 3vw, 1.25rem); }
.text-xl { font-size: clamp(1.25rem, 3.5vw, 1.5rem); }
.text-2xl { font-size: clamp(1.5rem, 4vw, 2rem); }
.text-3xl { font-size: clamp(2rem, 5vw, 3rem); }

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .btn, .page-link, .card {
    min-height: 44px;
    touch-action: manipulation;
  }

  .card:hover {
    transform: none;
  }
}

/* Unique Modern Dashboard Design */
.App {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background:
    linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #667eea 50%, #764ba2 75%, #f093fb 100%);
  position: relative;
  overflow-x: hidden;
  padding: clamp(0.5rem, 2vw, 1rem);
  box-sizing: border-box;
  margin: 0;
}

.App::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 90% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 60%),
    conic-gradient(from 0deg at 30% 70%, transparent 0deg, rgba(255, 255, 255, 0.1) 60deg, transparent 120deg);
  pointer-events: none;
  z-index: 0;
}

.App::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
  z-index: 1;
}

/* Unique Header Design */
.dashboard-header {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: clamp(16px, 4vw, 32px);
  padding: clamp(1rem, 4vw, 3rem);
  margin-bottom: clamp(1rem, 3vw, 2rem);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.dashboard-title {
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 50%, #e6f3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: -0.02em;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.dashboard-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 2;
}

/* Unique Card Design */
.card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: none;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: clamp(12px, 3vw, 24px);
  overflow: hidden;
  position: relative;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 5;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 100%;
  animation: gradientShift 4s ease infinite;
  transform: scaleX(0);
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: left;
}

.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.card:hover::before {
  transform: scaleX(1);
}

.card:hover::after {
  opacity: 1;
}

.card:hover {
  transform: translateY(-8px) scale(1.02) rotateX(2deg);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}

/* Unique Search Design */
.search-container {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: clamp(16px, 4vw, 28px);
  padding: clamp(1rem, 3vw, 2rem);
  margin-bottom: clamp(1rem, 3vw, 2rem);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 8;
  overflow: hidden;
}

.search-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.input-group {
  border-radius: clamp(20px, 5vw, 50px);
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.input-group-text {
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  padding: clamp(0.75rem, 2vw, 1.25rem) clamp(1rem, 3vw, 1.5rem);
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-control {
  border: none;
  padding: clamp(0.75rem, 2vw, 1.25rem) clamp(1rem, 3vw, 1.5rem);
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  font-weight: 500;
}

.form-control:focus {
  border: none;
  box-shadow:
    0 0 0 3px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 1);
  outline: none;
}

.form-control::placeholder {
  color: rgba(0, 0, 0, 0.5);
  font-weight: 400;
}

/* Pagination styling */
.pagination-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.pagination .page-link {
  border: none;
  margin: 0 4px;
  border-radius: 12px !important;
  padding: 12px 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.pagination .page-item.active .page-link {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.pagination .page-link:hover {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Loading spinner */
.spinner-border {
  width: 4rem;
  height: 4rem;
  border-width: 0.4rem;
}

.loading-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* User card enhancements */
.user-card-title {
  color: #667eea;
  font-weight: 600;
  margin-bottom: 1rem;
}

.user-card-info {
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.6;
}

.user-card-info strong {
  color: #495057;
  font-weight: 600;
}

.user-card-info a {
  color: #667eea;
  transition: color 0.2s ease;
}

.user-card-info a:hover {
  color: #764ba2;
  text-decoration: none;
}

/* Results info styling */
.results-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  text-align: center;
}

/* Alert styling */
.alert {
  border: none;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.alert-danger {
  background: rgba(220, 53, 69, 0.1);
  color: white;
}

/* Button styling */
.btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-outline-danger {
  border: 2px solid rgba(220, 53, 69, 0.5);
  color: white;
  background: rgba(220, 53, 69, 0.1);
}

.btn-outline-danger:hover {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.8);
  color: white;
}

.btn-outline-primary {
  border: 2px solid rgba(255, 255, 255, 0.5);
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.btn-outline-primary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.8);
  color: white;
}

.btn-outline-secondary {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #667eea;
}

.btn-outline-secondary:hover {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

/* No results styling */
.no-results {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  text-align: center;
}

.no-results i {
  color: rgba(255, 255, 255, 0.6);
}

/* Container & Layout Fixes */
.container-fluid {
  width: 100vw;
  max-width: 100vw;
  margin: 0;
  padding: clamp(0.5rem, 2vw, 1.5rem);
  box-sizing: border-box;
}

.row {
  margin-left: 0;
  margin-right: 0;
  width: 100%;
  box-sizing: border-box;
}

.row > * {
  padding-left: clamp(0.5rem, 1.5vw, 0.75rem);
  padding-right: clamp(0.5rem, 1.5vw, 0.75rem);
  box-sizing: border-box;
}

/* Responsive Grid System */
@media (max-width: 575.98px) {
  .container-fluid {
    padding: 0.5rem;
  }

  .row > * {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
}

@media (min-width: 1400px) {
  .container-fluid {
    max-width: 1320px;
    margin: 0 auto;
  }
}

/* Mobile-First Responsive Design */

/* Base Mobile Styles (320px+) */
.dashboard-header {
  padding: clamp(1rem, 4vw, 2rem);
  margin-bottom: clamp(1rem, 3vw, 2rem);
  border-radius: clamp(12px, 3vw, 24px);
}

.search-container {
  padding: clamp(0.75rem, 3vw, 1.5rem);
  margin-bottom: clamp(1rem, 3vw, 2rem);
  border-radius: clamp(12px, 3vw, 20px);
}

.card {
  margin-bottom: clamp(1rem, 3vw, 1.5rem);
  border-radius: clamp(12px, 3vw, 20px);
}

.input-group {
  border-radius: clamp(16px, 4vw, 40px);
}

.input-group-text, .form-control {
  padding: clamp(0.75rem, 2vw, 1.25rem) clamp(1rem, 3vw, 1.5rem);
  font-size: clamp(0.9rem, 2vw, 1.1rem);
}

/* Extra Small Devices (phones, up to 575px) */
@media (max-width: 575.98px) {
  .App {
    padding: 0.25rem;
  }

  .dashboard-title {
    font-size: clamp(1.25rem, 6vw, 1.75rem) !important;
    line-height: 1.2;
  }

  .dashboard-subtitle {
    font-size: clamp(0.8rem, 3vw, 1rem);
    line-height: 1.4;
  }

  .pagination .page-link {
    padding: 8px 12px;
    font-size: 0.8rem;
    margin: 0 1px;
    min-width: 40px;
    min-height: 40px;
  }

  .pagination .page-link span {
    display: none !important;
  }

  .card:hover {
    transform: translateY(-2px) scale(1.01);
  }

  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    min-height: 44px;
  }

  .spinner-border {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }
}

/* Small Devices (landscape phones, 576px+) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .dashboard-title {
    font-size: clamp(1.5rem, 4vw, 2rem) !important;
  }

  .pagination .page-link {
    padding: 10px 14px;
    font-size: 0.875rem;
    margin: 0 2px;
    min-width: 42px;
    min-height: 42px;
  }

  .card:hover {
    transform: translateY(-4px) scale(1.015);
  }
}

/* Medium Devices (tablets, 768px+) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .dashboard-title {
    font-size: clamp(2rem, 4vw, 2.5rem) !important;
  }

  .pagination .page-link {
    padding: 12px 16px;
    font-size: 0.9rem;
    margin: 0 3px;
  }

  .card:hover {
    transform: translateY(-6px) scale(1.02);
  }
}

/* Large Devices (desktops, 992px+) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .container-fluid {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 1rem clamp(1rem, 3vw, 3rem);
    box-sizing: border-box;
  }

  .dashboard-title {
    font-size: clamp(2.25rem, 3vw, 2.75rem) !important;
  }

  .card:hover {
    transform: translateY(-8px) scale(1.025) rotateX(1deg);
  }

  /* Ensure cards fill the available space */
  .row {
    justify-content: flex-start;
  }
}

/* Specific fix for 1170px range and white space issues */
@media (min-width: 1100px) and (max-width: 1300px) {
  .container-fluid {
    width: 100vw !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding-left: clamp(1rem, 2vw, 2rem) !important;
    padding-right: clamp(1rem, 2vw, 2rem) !important;
    box-sizing: border-box !important;
  }

  .row {
    width: 100% !important;
    margin: 0 !important;
    justify-content: space-between !important;
    gap: 0 !important;
  }

  /* Ensure cards use full available width with proper distribution */
  .col-xl-3 {
    flex: 1 1 calc(25% - 1rem);
    max-width: calc(25% - 0.5rem);
    min-width: 280px;
  }

  /* For rows with fewer than 4 cards, distribute evenly */
  .row .col-xl-3:only-child {
    max-width: 100%;
    flex: 1 1 100%;
  }

  .row .col-xl-3:nth-child(2):last-child,
  .row .col-xl-3:first-child:nth-last-child(2) {
    max-width: calc(50% - 0.5rem);
    flex: 1 1 calc(50% - 0.5rem);
  }

  .row .col-xl-3:nth-child(3):last-child,
  .row .col-xl-3:first-child:nth-last-child(3),
  .row .col-xl-3:nth-child(2):nth-last-child(2) {
    max-width: calc(33.333% - 0.5rem);
    flex: 1 1 calc(33.333% - 0.5rem);
  }
}

/* Extra Large Devices (large desktops, 1200px+) */
@media (min-width: 1200px) {
  .container-fluid {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 1.5rem clamp(1rem, 4vw, 4rem);
    box-sizing: border-box;
  }

  .dashboard-title {
    font-size: clamp(2.5rem, 3vw, 3.5rem) !important;
  }

  .card:hover {
    transform: translateY(-10px) scale(1.03) rotateX(2deg);
  }

  .pagination .page-link {
    padding: 14px 20px;
    font-size: 1rem;
    margin: 0 4px;
  }

  /* Ensure full width usage */
  .row {
    justify-content: flex-start;
  }
}

/* Ultra Wide Screens (1400px+) */
@media (min-width: 1400px) {
  .dashboard-title {
    font-size: clamp(3rem, 3vw, 4rem) !important;
  }

  .search-container, .dashboard-header {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Bootstrap Icons */
.bi::before {
  font-family: "bootstrap-icons" !important;
}

/* Utility classes */
.text-decoration-none:hover {
  text-decoration: underline !important;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Additional responsive utilities */
.text-truncate-mobile {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive spacing */
.py-responsive {
  padding-top: clamp(1rem, 4vw, 2rem);
  padding-bottom: clamp(1rem, 4vw, 2rem);
}

.px-responsive {
  padding-left: clamp(0.5rem, 2vw, 1rem);
  padding-right: clamp(0.5rem, 2vw, 1rem);
}

/* Responsive font sizes */
.fs-responsive-sm {
  font-size: clamp(0.75rem, 2vw, 0.875rem);
}

.fs-responsive-md {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
}

.fs-responsive-lg {
  font-size: clamp(1rem, 3vw, 1.25rem);
}

.fs-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 2rem);
}

/* Mobile-first card improvements */
@media (max-width: 575.98px) {
  .card-body {
    padding: 0.75rem !important;
  }

  .user-card-info a {
    word-break: break-all;
    font-size: 0.8rem;
  }

  .user-card-info strong {
    font-size: 0.85rem;
  }

  .border-top {
    margin-top: 0.75rem !important;
    padding-top: 0.75rem !important;
  }
}

/* Landscape phone optimizations */
@media (max-width: 767.98px) and (orientation: landscape) {
  .dashboard-header {
    padding: 0.75rem;
  }

  .dashboard-title {
    font-size: 1.25rem !important;
  }

  .search-container {
    padding: 0.5rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .card {
    border-radius: 18px;
  }

  .input-group {
    border-radius: 48px;
  }
}
