
/**
 * @param {Object} user
*/
const UserCard = ({ user }) => {
  if (!user) {
    return null;
  }

  return (
    <div className="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-3 col-xxl-2 mb-4">
      <div className="card h-100">
        <div className="card-body d-flex flex-column" style={{padding: 'clamp(1rem, 3vw, 1.5rem)', position: 'relative', zIndex: 2}}>
          <h5 className="text-lg mb-3" style={{color: 'rgba(255, 255, 255, 0.95)', fontWeight: '600', letterSpacing: '-0.01em'}}>
            <i className="bi bi-person-circle me-2" style={{fontSize: 'clamp(1.1rem, 3vw, 1.3rem)'}}></i>
            <span className="d-inline-block" style={{maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}}>
              {user.name}
            </span>
          </h5>

          <div className="card-text flex-grow-1" style={{color: 'rgba(255, 255, 255, 0.8)'}}>
            <div className="mb-3">
              <div className="text-sm" style={{color: 'rgba(255, 255, 255, 0.7)', fontWeight: '500', marginBottom: '0.25rem'}}>
                <i className="bi bi-envelope me-2"></i>
                Email
              </div>
              <a
                href={`mailto:${user.email}`}
                className="text-decoration-none text-sm"
                title={`Send email to ${user.name}`}
                style={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  wordBreak: 'break-all',
                  fontSize: 'clamp(0.8rem, 2vw, 0.9rem)',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => e.target.style.color = '#fff'}
                onMouseLeave={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.9)'}
              >
                {user.email}
              </a>
            </div>

            <div className="mb-3">
              <div className="text-sm" style={{color: 'rgba(255, 255, 255, 0.7)', fontWeight: '500', marginBottom: '0.25rem'}}>
                <i className="bi bi-telephone me-2"></i>
                Phone
              </div>
              <a
                href={`tel:${user.phone}`}
                className="text-decoration-none text-sm"
                title={`Call ${user.name}`}
                style={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  fontSize: 'clamp(0.8rem, 2vw, 0.9rem)',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => e.target.style.color = '#fff'}
                onMouseLeave={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.9)'}
              >
                {user.phone}
              </a>
            </div>

            {user.website && (
              <div className="mb-3">
                <div className="text-sm" style={{color: 'rgba(255, 255, 255, 0.7)', fontWeight: '500', marginBottom: '0.25rem'}}>
                  <i className="bi bi-globe me-2"></i>
                  Website
                </div>
                <a
                  href={`https://${user.website}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-decoration-none text-sm"
                  title={`Visit ${user.name}'s website`}
                  style={{
                    color: 'rgba(255, 255, 255, 0.9)',
                    wordBreak: 'break-all',
                    fontSize: 'clamp(0.8rem, 2vw, 0.9rem)',
                    transition: 'color 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.target.style.color = '#fff'}
                  onMouseLeave={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.9)'}
                >
                  {user.website}
                </a>
              </div>
            )}

            {user.company && (
              <div className="mb-3">
                <div className="text-sm" style={{color: 'rgba(255, 255, 255, 0.7)', fontWeight: '500', marginBottom: '0.25rem'}}>
                  <i className="bi bi-building me-2"></i>
                  Company
                </div>
                <span className="text-sm" style={{color: 'rgba(255, 255, 255, 0.9)', fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'}}>
                  {user.company.name}
                </span>
              </div>
            )}
          </div>

          <div className="mt-auto pt-3" style={{borderTop: '1px solid rgba(255, 255, 255, 0.2)'}}>
            <div className="text-xs d-flex align-items-center" style={{color: 'rgba(255, 255, 255, 0.6)'}}>
              <i className="bi bi-geo-alt me-2"></i>
              <span>{user.address?.city}, {user.address?.zipcode}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserCard;
